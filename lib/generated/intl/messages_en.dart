// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(name) => "مرحباً، ${name}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about": MessageLookupByLibrary.simpleMessage("حول"),
    "activeShipments": MessageLookupByLibrary.simpleMessage("الشحنات النشطة"),
    "address": MessageLookupByLibrary.simpleMessage("العنوان"),
    "animals": MessageLookupByLibrary.simpleMessage("الحيوانات"),
    "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
    "cannotSelectSameStation": MessageLookupByLibrary.simpleMessage(
      "لا يمكن اختيار نفس المحطة للانطلاق والتوصيل",
    ),
    "changeLocation": MessageLookupByLibrary.simpleMessage("تغيير الموقع"),
    "changeSocial": MessageLookupByLibrary.simpleMessage("تغيير وسائل التواصل"),
    "close": MessageLookupByLibrary.simpleMessage("إغلاق"),
    "comingSoon": MessageLookupByLibrary.simpleMessage("قريباً..."),
    "confirm": MessageLookupByLibrary.simpleMessage("تأكيد"),
    "confirmOrder": MessageLookupByLibrary.simpleMessage("انشاء الطلب"),
    "confirmOrderCreation": MessageLookupByLibrary.simpleMessage(
      "انشاء الشحنة",
    ),
    "confirmPassword": MessageLookupByLibrary.simpleMessage(
      "تأكيد كلمة المرور",
    ),
    "createAccount": MessageLookupByLibrary.simpleMessage("إنشاء حساب"),
    "createOrder": MessageLookupByLibrary.simpleMessage("انشاء شحنة"),
    "createOrderTitle": MessageLookupByLibrary.simpleMessage("انشاء شحنة"),
    "dataLoadError": MessageLookupByLibrary.simpleMessage(
      "خطأ في تحميل البيانات",
    ),
    "deliveryNote": MessageLookupByLibrary.simpleMessage(
      "يجب تسليم الشحنة في حال تم انشائها في نصف ساعه",
    ),
    "deliveryStation": MessageLookupByLibrary.simpleMessage("محطة التوصيل"),
    "departureStation": MessageLookupByLibrary.simpleMessage("محطة الانطلاق"),
    "description": MessageLookupByLibrary.simpleMessage("الوصف"),
    "didntReceiveCode": MessageLookupByLibrary.simpleMessage(
      "لم تتلق رمز التحقق؟",
    ),
    "doctorBackground": MessageLookupByLibrary.simpleMessage("خلفية الطبيب"),
    "doctorLogo": MessageLookupByLibrary.simpleMessage("شعار الطبيب"),
    "doctorName": MessageLookupByLibrary.simpleMessage("اسم الطبيب"),
    "doctors": MessageLookupByLibrary.simpleMessage("الأطباء"),
    "dontHaveAccount": MessageLookupByLibrary.simpleMessage("لا تملك حساب؟"),
    "dontHaveAnAccount": MessageLookupByLibrary.simpleMessage("ليس لديك حساب؟"),
    "editData": MessageLookupByLibrary.simpleMessage("تعديل بيانات"),
    "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
    "emailOptional": MessageLookupByLibrary.simpleMessage(
      "البريد الإلكتروني (اختياري)",
    ),
    "enter": MessageLookupByLibrary.simpleMessage("أدخل"),
    "enterVerificationCode": MessageLookupByLibrary.simpleMessage(
      "أدخل رمز التحقق",
    ),
    "error": MessageLookupByLibrary.simpleMessage("خطأ"),
    "forgotPassword": MessageLookupByLibrary.simpleMessage("نسيت كلمة المرور؟"),
    "forgotPasswordLink": MessageLookupByLibrary.simpleMessage(
      "نسيت كلمة المرور؟",
    ),
    "from": MessageLookupByLibrary.simpleMessage("من: "),
    "fullName": MessageLookupByLibrary.simpleMessage("الاسم الكامل"),
    "haveAnAccount": MessageLookupByLibrary.simpleMessage("لديك حساب؟"),
    "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
    "homePage": MessageLookupByLibrary.simpleMessage("الصفحة الرئيسية"),
    "idNumber": MessageLookupByLibrary.simpleMessage("رقم الهوية"),
    "invalidIdNumber": MessageLookupByLibrary.simpleMessage(
      "رقم الهوية غير صحيح",
    ),
    "invalidPhoneFormat": MessageLookupByLibrary.simpleMessage(
      "تنسيق رقم الهاتف غير صحيح",
    ),
    "invalidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "رقم الهاتف غير صحيح",
    ),
    "invalidVerificationCode": MessageLookupByLibrary.simpleMessage(
      "رمز التحقق غير صحيح",
    ),
    "locationPickedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم اختيار الموقع بنجاح",
    ),
    "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "loginSuccessful": MessageLookupByLibrary.simpleMessage(
      "تم تسجيل الدخول بنجاح",
    ),
    "loginTitle": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "loginWithYourAccountNow": MessageLookupByLibrary.simpleMessage(
      "سجل دخولك بحسابك الآن!",
    ),
    "menu": MessageLookupByLibrary.simpleMessage("المنيو"),
    "mobileNumber": MessageLookupByLibrary.simpleMessage("رقم الهاتف المحمول"),
    "myReceivedShipments": MessageLookupByLibrary.simpleMessage(
      "شحناتي المستلمة",
    ),
    "mySentShipments": MessageLookupByLibrary.simpleMessage("شحناتي المرسلة"),
    "next": MessageLookupByLibrary.simpleMessage("التالي"),
    "noDataFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على بيانات",
    ),
    "noReceivedShipments": MessageLookupByLibrary.simpleMessage(
      "لا توجد شحنات مستلمة",
    ),
    "noSentShipments": MessageLookupByLibrary.simpleMessage(
      "لا توجد شحنات مرسلة",
    ),
    "note": MessageLookupByLibrary.simpleMessage("ملحوظة"),
    "notes": MessageLookupByLibrary.simpleMessage("ملاحظات"),
    "onBoardingDescription1": MessageLookupByLibrary.simpleMessage(
      "اكتشف مجموعة واسعة من الحيوانات الأليفة للتبني واصطحب الرفيق المثالي إلى المنزل.",
    ),
    "onBoardingDescription2": MessageLookupByLibrary.simpleMessage(
      "تصفح متاجر الحيوانات الأليفة الموثوقة واعثر على أفضل المنتجات لأصدقائك ذوي الفراء.",
    ),
    "onBoardingDescription3": MessageLookupByLibrary.simpleMessage(
      "تواصل مع الأطباء البيطريين المحترفين وتأكد من حصول حيواناتك الأليفة على أفضل رعاية.",
    ),
    "onBoardingTitle1": MessageLookupByLibrary.simpleMessage(
      "اعثر على حيوانك الأليف المثالي",
    ),
    "onBoardingTitle2": MessageLookupByLibrary.simpleMessage(
      "استكشف أفضل متاجر الحيوانات الأليفة",
    ),
    "onBoardingTitle3": MessageLookupByLibrary.simpleMessage(
      "اعتن بصحة حيوانك الأليف",
    ),
    "orderCreatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إنشاء الطلب بنجاح",
    ),
    "orderSummary": MessageLookupByLibrary.simpleMessage("ملخص الطلب"),
    "orderType": MessageLookupByLibrary.simpleMessage("النوع"),
    "pageUnderDevelopment": MessageLookupByLibrary.simpleMessage(
      "هذه الصفحة قيد التطوير",
    ),
    "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
    "passwordsDoNotMatch": MessageLookupByLibrary.simpleMessage(
      "كلمات المرور غير متطابقة",
    ),
    "phoneHint": MessageLookupByLibrary.simpleMessage("05xxxxxxxx"),
    "pickImage": MessageLookupByLibrary.simpleMessage("اختيار صورة"),
    "pickLocation": MessageLookupByLibrary.simpleMessage("اختيار الموقع"),
    "pleaseAcceptTerms": MessageLookupByLibrary.simpleMessage(
      "يرجى الموافقة على الشروط والأحكام",
    ),
    "pleaseAddValidLink": MessageLookupByLibrary.simpleMessage(
      "يرجى إضافة رابط صحيح",
    ),
    "pleaseAddYourLocation": MessageLookupByLibrary.simpleMessage(
      "يرجى إضافة موقعك",
    ),
    "pleaseAddYourSocialMedia": MessageLookupByLibrary.simpleMessage(
      "يرجى إضافة وسائل التواصل الاجتماعي الخاصة بك",
    ),
    "pleaseEnterReceiverName": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال اسم المستلم",
    ),
    "pleaseEnterReceiverPhone": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال رقم المستلم",
    ),
    "pleaseSelectDeliveryStation": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار محطة التوصيل",
    ),
    "pleaseSelectDepartureStation": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار محطة الانطلاق",
    ),
    "pleaseSelectOrderType": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار نوع الطلب",
    ),
    "points": MessageLookupByLibrary.simpleMessage("نقطة"),
    "price": MessageLookupByLibrary.simpleMessage("السعر: "),
    "products": MessageLookupByLibrary.simpleMessage("المنتجات"),
    "receiverName": MessageLookupByLibrary.simpleMessage("اسم المستلم"),
    "receiverPhone": MessageLookupByLibrary.simpleMessage("رقم المستلم"),
    "reels": MessageLookupByLibrary.simpleMessage("الريلز"),
    "register": MessageLookupByLibrary.simpleMessage("التسجيل"),
    "registerAsDoctor": MessageLookupByLibrary.simpleMessage("التسجيل كطبيب؟"),
    "registerAsStore": MessageLookupByLibrary.simpleMessage("التسجيل كمتجر؟"),
    "registerWithYourAccountNow": MessageLookupByLibrary.simpleMessage(
      "سجل حسابك الآن!",
    ),
    "registrationSuccessful": MessageLookupByLibrary.simpleMessage(
      "تم التسجيل بنجاح",
    ),
    "rememberMe": MessageLookupByLibrary.simpleMessage("تذكرني"),
    "resendCode": MessageLookupByLibrary.simpleMessage("إعادة إرسال الرمز"),
    "resetPassword": MessageLookupByLibrary.simpleMessage(
      "إعادة تعيين كلمة المرور",
    ),
    "save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "search": MessageLookupByLibrary.simpleMessage("البحث"),
    "searchForDoctors": MessageLookupByLibrary.simpleMessage(
      "البحث عن الأطباء",
    ),
    "searchForProducts": MessageLookupByLibrary.simpleMessage(
      "البحث عن المنتجات",
    ),
    "searchForStores": MessageLookupByLibrary.simpleMessage("البحث عن المتاجر"),
    "searchOrder": MessageLookupByLibrary.simpleMessage("البحث عن شحنة"),
    "seeAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
    "shipmentsHistory": MessageLookupByLibrary.simpleMessage("سجل الشحنات"),
    "shops": MessageLookupByLibrary.simpleMessage("المتاجر"),
    "skip": MessageLookupByLibrary.simpleMessage("تخطي"),
    "socialMedia": MessageLookupByLibrary.simpleMessage(
      "وسائل التواصل الاجتماعي",
    ),
    "startNow": MessageLookupByLibrary.simpleMessage("ابدأ الآن"),
    "storeBackground": MessageLookupByLibrary.simpleMessage("خلفية المتجر"),
    "storeLogo": MessageLookupByLibrary.simpleMessage("شعار المتجر"),
    "storeName": MessageLookupByLibrary.simpleMessage("اسم المتجر"),
    "stores": MessageLookupByLibrary.simpleMessage("المتاجر"),
    "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
    "tapToSelectLocation": MessageLookupByLibrary.simpleMessage(
      "اضغط لاختيار الموقع",
    ),
    "termsAndConditions": MessageLookupByLibrary.simpleMessage(
      "أقر بأنني قد قرأت ووافقت على الشروط والأحكام الخاصة باستخدام هذا التطبيق",
    ),
    "termsAndConditionsEnd": MessageLookupByLibrary.simpleMessage(
      "الخاصة باستخدام هذا التطبيق",
    ),
    "termsAndConditionsLink": MessageLookupByLibrary.simpleMessage(
      "الشروط والأحكام",
    ),
    "termsAndConditionsText": MessageLookupByLibrary.simpleMessage(
      "أقر بأنني قد قرأت وفهمت وأوافق على",
    ),
    "termsDialogContent": MessageLookupByLibrary.simpleMessage(
      "هذه هي الشروط والأحكام الخاصة بالتطبيق. يرجى قراءتها بعناية قبل الموافقة عليها. سيتم إضافة المحتوى الفعلي للشروط والأحكام هنا لاحقاً.",
    ),
    "termsDialogTitle": MessageLookupByLibrary.simpleMessage("الشروط والأحكام"),
    "to": MessageLookupByLibrary.simpleMessage("إلى: "),
    "verificationCodeSent": MessageLookupByLibrary.simpleMessage(
      "تم إرسال رمز التحقق بنجاح",
    ),
    "verificationMessage": MessageLookupByLibrary.simpleMessage(
      "تم إرسال رمز التحقق إلى رقم هاتفك",
    ),
    "verificationTitle": MessageLookupByLibrary.simpleMessage(
      "التحقق من الهاتف",
    ),
    "verify": MessageLookupByLibrary.simpleMessage("تحقق"),
    "wallet": MessageLookupByLibrary.simpleMessage("المحفظة"),
    "welcomeWithName": m0,
    "youCanAlsoRegisterAsDoctor": MessageLookupByLibrary.simpleMessage(
      "يمكنك أيضاً التسجيل كطبيب من ملفك الشخصي.",
    ),
    "youCanAlsoRegisterAsStore": MessageLookupByLibrary.simpleMessage(
      "يمكنك أيضاً التسجيل كمتجر من ملفك الشخصي.",
    ),
  };
}
