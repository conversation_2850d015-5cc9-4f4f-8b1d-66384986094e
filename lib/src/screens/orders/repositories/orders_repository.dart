import 'package:dropx/src/core/consts/network/api_endpoints.dart';
import 'package:dropx/src/screens/orders/models/station.model.dart';
import 'package:dropx/src/screens/orders/models/type.model.dart';
import 'package:dropx/src/screens/orders/models/pricing.model.dart';
import 'package:dropx/src/screens/orders/models/create_order_request.model.dart';
import 'package:dropx/src/screens/orders/models/orders_response.model.dart';
import 'package:xr_helper/xr_helper.dart';

class OrdersRepository with BaseRepository {
  final BaseApiServices networkApiService;

  OrdersRepository({
    required this.networkApiService,
  });

  // * Get Orders
  Future<OrdersResponseModel> getOrders() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.orders;

        final response = await networkApiService.getResponse(url);

        final ordersResponse = OrdersResponseModel.fromJson(response);

        return ordersResponse;
      },
    );
  }

  // * Get Stations
  Future<List<Station>> getStations() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.stations;

        final response = await networkApiService.getResponse(url);

        final List<dynamic> data = response['data'];
        return data.map((json) => Station.fromJson(json)).toList();
      },
    );
  }

  // * Get Types
  Future<List<OrderType>> getTypes() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.types;

        final response = await networkApiService.getResponse(url);

        final List<dynamic> data = response['data'];
        return data.map((json) => OrderType.fromJson(json)).toList();
      },
    );
  }

  // * Get Pricing
  Future<Pricing> getPricing({
    required int fromStationId,
    required int toStationId,
    required int typeId,
  }) async {
    return baseFunction(
      () async {
        final url =
            '${ApiEndpoints.pricing}?from_station_id=$fromStationId&to_station_id=$toStationId&type_id=$typeId';

        final response = await networkApiService.getResponse(url);

        return Pricing.fromJson(response['data']);
      },
    );
  }

  // * Create Order (with CreateOrderRequest)
  Future<bool> createOrder({
    required CreateOrderRequest orderRequest,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.orders;

        await networkApiService.postResponse(url, body: orderRequest.toJson());

        return true;
      },
    );
  }

  // * Create Order (with Map - for backward compatibility)
  Future<void> createOrderWithMap({
    required Map<String, dynamic> data,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.orders;

        await networkApiService.postResponse(
          url,
          body: data,
        );
      },
    );
  }
}
