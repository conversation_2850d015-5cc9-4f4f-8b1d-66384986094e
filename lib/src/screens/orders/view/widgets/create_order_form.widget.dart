import 'package:dropx/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/consts/network/api_strings.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/fields/text_field.dart';
import 'package:dropx/src/core/shared/widgets/drop_downs/station_dropdown.dart';
import 'package:dropx/src/core/shared/widgets/drop_downs/type_dropdown.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/orders/models/station.model.dart';
import 'package:dropx/src/screens/orders/models/type.model.dart';
import 'package:dropx/src/screens/orders/models/create_order_request.model.dart';
import 'package:dropx/src/screens/orders/providers/orders_providers.dart';
import 'package:xr_helper/xr_helper.dart';
import '../../../../core/consts/app_constants.dart';
import 'order_confirmation_dialog.widget.dart';

class CreateOrderFormWidget extends HookConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;

  const CreateOrderFormWidget({
    super.key,
    required this.formKey,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final departureStation = useState<Station?>(null);
    final deliveryStation = useState<Station?>(null);
    final orderType = useState<OrderType?>(null);
    final isLoading = useState(false);
    final isFormValid = useState(false);

    // Form validation state
    final departureStationValid = useState(false);
    final deliveryStationValid = useState(false);
    final receiverNameValid = useState(false);
    final receiverPhoneValid = useState(false);
    final orderTypeValid = useState(false);

    // Update form validity
    useEffect(() {
      isFormValid.value = departureStationValid.value &&
          deliveryStationValid.value &&
          receiverNameValid.value &&
          receiverPhoneValid.value &&
          orderTypeValid.value &&
          (departureStation.value?.id != deliveryStation.value?.id);
      return null;
    }, [
      departureStationValid.value,
      deliveryStationValid.value,
      receiverNameValid.value,
      receiverPhoneValid.value,
      orderTypeValid.value,
      departureStation.value,
      deliveryStation.value,
    ]);

    // Update station validation when stations change
    useEffect(() {
      departureStationValid.value = departureStation.value != null;
      deliveryStationValid.value = deliveryStation.value != null;
      return null;
    }, [departureStation.value, deliveryStation.value]);

    // Update type validation when type changes
    useEffect(() {
      orderTypeValid.value = orderType.value != null;
      return null;
    }, [orderType.value]);

    void createOrder() {
      if (!formKey.currentState!.saveAndValidate()) return;

      if (!_validateStations(
          context, departureStation.value, deliveryStation.value)) {
        return;
      }

      final data = formKey.currentState?.instantValue ?? {};

      // validate type
      if (orderType.value == null) {
        showToast(context.tr.pleaseSelectOrderType, isError: true);
        return;
      }

      _handleCreateOrder(
        context,
        ref,
        departureStation,
        deliveryStation,
        orderType,
        data,
        isLoading,
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Departure Station
          Text(
            context.tr.departureStation,
            style: AppTextStyles.labelLarge.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.black,
            ),
          ),
          AppGaps.gap8,
          StationDropdown(
            label: context.tr.departureStation,
            valueNotifier: departureStation,
          ),
          AppGaps.gap16,

          // Delivery Station
          Text(
            context.tr.deliveryStation,
            style: AppTextStyles.labelLarge.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.black,
            ),
          ),
          AppGaps.gap8,
          StationDropdown(
            label: context.tr.deliveryStation,
            valueNotifier: deliveryStation,
          ),
          AppGaps.gap16,

          // Receiver Name
          BaseTextField(
            name: FieldsConsts.receiverName,
            title: context.tr.receiverName,
            textInputType: TextInputType.text,
            validator: (value) => Validations.mustBeNotEmpty(
              value,
              emptyMessage: context.tr.pleaseEnterReceiverName,
            ),
            realTimeValidator: (value) {
              final isValid = Validations.mustBeNotEmpty(value) == null;
              receiverNameValid.value = isValid;
              return null; // Don't show error in real-time for name
            },
          ),
          AppGaps.gap16,

          // Receiver Phone
          BaseTextField(
            name: FieldsConsts.receiverPhone,
            title: context.tr.receiverPhone,
            textInputType: TextInputType.phone,
            validator: (value) => Validations.palestinianPhoneNumber(
              value,
              emptyMessage: context.tr.pleaseEnterReceiverPhone,
              invalidMessage: context.tr.invalidPhoneFormat,
            ),
            realTimeValidator: (value) {
              final error = Validations.palestinianPhoneNumber(
                value,
                emptyMessage: context.tr.pleaseEnterReceiverPhone,
                invalidMessage: context.tr.invalidPhoneFormat,
              );
              receiverPhoneValid.value = error == null;
              return error;
            },
          ),
          AppGaps.gap16,

          // Order Type
          Text(
            context.tr.orderType,
            style: AppTextStyles.labelLarge.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.black,
            ),
          ),
          AppGaps.gap8,
          TypeDropdown(
            label: context.tr.orderType,
            valueNotifier: orderType,
          ),
          AppGaps.gap16,

          // Notes
          BaseTextField(
            name: FieldsConsts.notes,
            title: context.tr.notes,
            textInputType: TextInputType.multiline,
            maxLines: 3,
            isRequired: false,
          ),
          AppGaps.gap16,

          // Delivery Note
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: '${context.tr.note}: ',
                    style: AppTextStyles.labelMedium.copyWith(
                      color: Colors.red,
                      fontFamily: AppConsts.fontFamily,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextSpan(
                    text: context.tr.deliveryNote,
                    style: AppTextStyles.labelMedium.copyWith(
                      fontFamily: AppConsts.fontFamily,
                    ),
                  ),
                ],
              ),
            ),
          ),

          AppGaps.gap24,

          // Create Order Button
          Button(
            label: context.tr.confirmOrder,
            onPressed: createOrder,
            isLoading: isLoading.value,
            loadingWidget: LoadingWidget(),
            color: ColorManager.primaryColor,
          ),
        ],
      ),
    );
  }

  bool _validateStations(
      BuildContext context, Station? departure, Station? delivery) {
    if (departure == null) {
      showToast(context.tr.pleaseSelectDepartureStation, isError: true);
      return false;
    }

    if (delivery == null) {
      showToast(context.tr.pleaseSelectDeliveryStation, isError: true);
      return false;
    }

    if (departure.id == delivery.id) {
      showToast(context.tr.cannotSelectSameStation, isError: true);
      return false;
    }

    return true;
  }

  Future<void> _handleCreateOrder(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<Station?> departureStation,
    ValueNotifier<Station?> deliveryStation,
    ValueNotifier<OrderType?> orderType,
    Map<String, dynamic> formData,
    ValueNotifier<bool> isLoading,
  ) async {
    try {
      isLoading.value = true;

      // Get pricing first
      final pricing = await ref.read(getPricingFutureProvider({
        'fromStationId': departureStation.value!.id,
        'toStationId': deliveryStation.value!.id,
        'typeId': orderType.value!.id,
      }).future);

      // Show confirmation dialog
      if (context.mounted) {
        final confirmed = await showDialog<bool>(
          context: context,
          builder: (context) => OrderConfirmationDialog(
            departureStation: departureStation.value!,
            deliveryStation: deliveryStation.value!,
            orderType: orderType.value!,
            receiverName: formData[FieldsConsts.receiverName] ?? '',
            receiverPhone: formData[FieldsConsts.receiverPhone] ?? '',
            notes: formData[FieldsConsts.notes] ?? '',
            pricing: pricing,
          ),
        );

        if (confirmed == true) {
          // Create order
          final orderRequest = CreateOrderRequest(
            fromStationId: departureStation.value!.id,
            toStationId: deliveryStation.value!.id,
            typeId: orderType.value!.id,
            receiverName: formData[FieldsConsts.receiverName] ?? '',
            receiverPhone: formData[FieldsConsts.receiverPhone] ?? '',
            price: pricing.price,
            note: formData[FieldsConsts.notes]?.isEmpty == true
                ? null
                : formData[FieldsConsts.notes],
          );

          final ordersController = ref.read(ordersControllerProvider);
          await ordersController.createOrder(orderRequest: orderRequest);

          // Show success message and navigate back
          if (context.mounted) {
            ref.invalidate(getOrdersFutureProvider);
            Navigator.of(context).pop();
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        showToast(context.tr.error, isError: true);
      }
    } finally {
      isLoading.value = false;
    }
  }
}
