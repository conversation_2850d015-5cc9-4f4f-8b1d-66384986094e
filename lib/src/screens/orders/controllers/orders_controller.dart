import 'package:dropx/src/screens/orders/models/station.model.dart';
import 'package:dropx/src/screens/orders/models/type.model.dart';
import 'package:dropx/src/screens/orders/models/pricing.model.dart';
import 'package:dropx/src/screens/orders/models/create_order_request.model.dart';
import 'package:dropx/src/screens/orders/models/orders_response.model.dart';
import 'package:dropx/src/screens/orders/repositories/orders_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class OrdersController extends BaseVM {
  final OrdersRepository ordersRepo;

  OrdersController({
    required this.ordersRepo,
  });

  // * Get Orders
  Future<OrdersResponseModel> getOrders() async {
    return await baseFunction(
      () async {
        return await ordersRepo.getOrders();
      },
    );
  }

  // * Get Stations
  Future<List<Station>> getStations() async {
    return await baseFunction(
      () async {
        final stations = await ordersRepo.getStations();
        return stations;
      },
    );
  }

  // * Get Types
  Future<List<OrderType>> getTypes() async {
    return await baseFunction(
      () async {
        final types = await ordersRepo.getTypes();
        return types;
      },
    );
  }

  // * Get Pricing
  Future<Pricing> getPricing({
    required int fromStationId,
    required int toStationId,
    required int typeId,
  }) async {
    return await baseFunction(
      () async {
        final pricing = await ordersRepo.getPricing(
          fromStationId: fromStationId,
          toStationId: toStationId,
          typeId: typeId,
        );
        return pricing;
      },
    );
  }

  // * Create Order (with CreateOrderRequest)
  Future<bool> createOrder({
    required CreateOrderRequest orderRequest,
  }) async {
    return await baseFunction(
      () async {
        final result = await ordersRepo.createOrder(orderRequest: orderRequest);
        return result;
      },
      additionalFunction: () {
        // Navigate back or show success message
      },
    );
  }

  // * Create Order (with Map - for backward compatibility)
  Future<void> createOrderWithMap({
    required Map<String, dynamic> data,
  }) async {
    return await baseFunction(
      () async {
        return await ordersRepo.createOrderWithMap(data: data);
      },
    );
  }
}
