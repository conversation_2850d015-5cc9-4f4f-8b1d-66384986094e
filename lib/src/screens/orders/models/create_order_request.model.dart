class CreateOrderRequest {
  final int fromStationId;
  final int toStationId;
  final int typeId;
  final String receiverName;
  final String receiverPhone;
  final String price;
  final String? note;

  CreateOrderRequest({
    required this.fromStationId,
    required this.toStationId,
    required this.typeId,
    required this.receiverName,
    required this.receiverPhone,
    required this.price,
    this.note,
  });

  Map<String, dynamic> toJson() {
    return {
      'from_station_id': fromStationId,
      'to_station_id': toStationId,
      'type_id': typeId,
      'receiver_name': receiverName,
      'receiver_phone': receiverPhone,
      'price': price,
      'note': note,
    };
  }
}
