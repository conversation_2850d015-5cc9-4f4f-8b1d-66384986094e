class Pricing {
  final int id;
  final String fromStation;
  final String toStation;
  final String type;
  final String price;
  final String createdAt;
  final String updatedAt;

  Pricing({
    required this.id,
    required this.fromStation,
    required this.toStation,
    required this.type,
    required this.price,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Pricing.fromJson(Map<String, dynamic> json) {
    return Pricing(
      id: json['id'] as int,
      fromStation: json['from_station'] as String,
      toStation: json['to_station'] as String,
      type: json['type'] as String,
      price: json['price'] as String,
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'from_station': fromStation,
      'to_station': toStation,
      'type': type,
      'price': price,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}
