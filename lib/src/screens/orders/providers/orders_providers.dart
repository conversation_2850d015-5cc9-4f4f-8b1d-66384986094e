import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/shared/providers/network_api_service_provider.dart';
import '../controllers/orders_controller.dart';
import '../repositories/orders_repository.dart';

// * Orders Repo Provider ========================================
final ordersRepoProvider = Provider<OrdersRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return OrdersRepository(networkApiService: networkApiService);
});

// * Orders Controller Provider ========================================
final ordersControllerProvider = Provider<OrdersController>(
  (ref) {
    final ordersRepo = ref.watch(ordersRepoProvider);

    return OrdersController(
      ordersRepo: ordersRepo,
    );
  },
);

// * Get Stations Future Provider ========================================
final getStationsFutureProvider = FutureProvider(
  (ref) {
    final ordersController = ref.watch(ordersControllerProvider);

    return ordersController.getStations();
  },
);

// * Get Types Future Provider ========================================
final getTypesFutureProvider = FutureProvider(
  (ref) {
    final ordersController = ref.watch(ordersControllerProvider);

    return ordersController.getTypes();
  },
);

// * Get Pricing Future Provider ========================================
final getPricingFutureProvider =
    FutureProvider.family<dynamic, Map<String, int>>(
  (ref, params) {
    final ordersController = ref.watch(ordersControllerProvider);

    return ordersController.getPricing(
      fromStationId: params['fromStationId']!,
      toStationId: params['toStationId']!,
      typeId: params['typeId']!,
    );
  },
);

// * Get Orders Future Provider ========================================
final getOrdersFutureProvider = FutureProvider(
  (ref) {
    final ordersController = ref.watch(ordersControllerProvider);

    return ordersController.getOrders();
  },
);
