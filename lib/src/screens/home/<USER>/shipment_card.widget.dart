import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/orders/models/order.model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../generated/assets.gen.dart';

class ShipmentCardWidget extends StatelessWidget {
  final OrderModel order;
  final VoidCallback? onTap;

  const ShipmentCardWidget({
    super.key,
    required this.order,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: 16.w,
        ),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: ColorManager.lightGrey,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              spreadRadius: 0,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Package Icon
            Assets.icons.package.image(),

            AppGaps.gap16,

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // From - To
                  Row(
                    children: [
                      Text(
                        context.tr.from,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: ColorManager.greyText,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          order.fromStation,
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),

                  AppGaps.gap4,

                  Row(
                    children: [
                      Text(
                        context.tr.to,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: ColorManager.greyText,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          order.toStation,
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),

                  AppGaps.gap8,

                  // Price
                  Row(
                    children: [
                      Text(
                        context.tr.price,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: ColorManager.greyText,
                        ),
                      ),
                      Text(
                        '${order.price} ${context.tr.points}',
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: ColorManager.primaryColor,
                        ),
                      ),
                    ],
                  ),

                  AppGaps.gap8,

                  // Status
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 12.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(order.status).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8.r),
                          border: Border.all(
                            color: _getStatusColor(order.status),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          order.statusText ??
                              order.latestTracking?.statusText ??
                              order.status,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: _getStatusColor(order.status),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Assets.icons.pendingPackage.image(
                        width: 40,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'shipped':
        return Colors.purple;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return ColorManager.greyText;
    }
  }
}
